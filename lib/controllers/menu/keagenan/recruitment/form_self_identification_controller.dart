import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/controllers/menu/keagenan/recruitment/recruitment_form_controller.dart';
import 'package:pdl_superapp/models/combo_box_models.dart';
import 'package:pdl_superapp/models/recruitment_draft_models.dart';
import 'package:pdl_superapp/models/recruitment_form_model.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:pdl_superapp/utils/form_validation.dart';

class FormSelfIdentificationController extends BaseControllers {
  final RecruitmentFormController baseController;
  FormSelfIdentificationController({required this.baseController});

  // FormSelfIdentification
  final emailController = TextEditingController();
  final nomorHpController = TextEditingController();

  // Emergency Contact
  final emergencyNamaController = TextEditingController();
  final emergencyHubunganController = TextEditingController();
  final emergencyNomorHpController = TextEditingController();

  // Bank
  final namaPemilikRekeningController = TextEditingController();
  final nomorRekeningController = TextEditingController();
  final namaBankController = TextEditingController();

  // Combo Box
  RxList<ComboBoxAgentLevel> bankList = RxList();
  RxBool isBankLoading = true.obs;

  RxString formUuId = ''.obs;

  // Validation errors
  RxString emailError = ''.obs;
  RxString nomorHpError = ''.obs;
  RxString emergencyNamaError = ''.obs;
  RxString emergencyHubunganError = ''.obs;
  RxString emergencyNomorHpError = ''.obs;
  RxString namaPemilikRekeningError = ''.obs;
  RxString nomorRekeningError = ''.obs;
  RxString namaBankError = ''.obs;

  @override
  void onInit() {
    super.onInit();
    _setupFormChangeListeners();

    api.getComboCategoryById(
      controllers: this,
      key: 'Bank',
      code: kReqGetComboBoxBank,
    );
  }

  @override
  void loadSuccess({
    required int requestCode,
    required response,
    required int statusCode,
  }) {
    super.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );
    setLoading(false);
    switch (requestCode) {
      case kReqGetComboBoxBank:
        parseDataBank(response);
        break;
      case kReqSaveDraft:
        baseController.submitForm();
        parseDataSubmitDraft(response);
        break;
      default:
    }
  }

  void parseDataSubmitDraft(response) {
    RecruitmentDraftModels draftData = RecruitmentDraftModels.fromJson(
      response,
    );
    log('Draft data: $draftData');
    print('here uuID ${draftData.uuid}');
    formUuId.value = draftData.uuid ?? '';
    // 2. send email verification
    // 3. popup email verification page (full page)
    baseController.isVerificationEmailSent.value = true;
  }

  void parseDataBank(response) {
    bankList.clear();
    bankList.assignAll(
      (response as List)
          .map((item) => ComboBoxAgentLevel.fromJSon(item))
          .toList(),
    );

    isBankLoading.value = false;
  }

  // Setup listener untuk perubahan form
  void _setupFormChangeListeners() {
    // Tambahkan listener untuk FormSelfIdentification
    emailController.addListener(_onFormChanged);
    nomorHpController.addListener(_onFormChanged);
    emergencyNamaController.addListener(_onFormChanged);
    emergencyHubunganController.addListener(_onFormChanged);
    emergencyNomorHpController.addListener(_onFormChanged);
    namaPemilikRekeningController.addListener(_onFormChanged);
    nomorRekeningController.addListener(_onFormChanged);
    namaBankController.addListener(_onFormChanged);
  }

  // Handler ketika form berubah
  void _onFormChanged() {
    // Notify parent controller about form change
    try {
      baseController.onFormChanged();
    } catch (e) {
      log('Parent controller not found: $e');
    }
  }

  // Validate form self identification
  bool validateForm() {
    bool isValid = true;

    // Clear previous errors
    _clearAllErrors();

    // Validate email
    final emailError = FormValidation.validateEmail(emailController.text);
    if (emailError != null) {
      this.emailError.value = emailError;
      isValid = false;
    }

    // Validate phone number
    final phoneError = FormValidation.validatePhoneNumber(
      nomorHpController.text,
    );
    if (phoneError != null) {
      nomorHpError.value = phoneError;
      isValid = false;
    }

    // Validate emergency contact name
    final emergencyNameError = FormValidation.validateRequired(
      emergencyNamaController.text,
      'Nama kontak darurat',
    );
    if (emergencyNameError != null) {
      emergencyNamaError.value = emergencyNameError;
      isValid = false;
    }

    // Validate emergency contact relation
    final emergencyRelationError = FormValidation.validateRequired(
      emergencyHubunganController.text,
      'Hubungan dengan Anda',
    );
    if (emergencyRelationError != null) {
      emergencyHubunganError.value = emergencyRelationError;
      isValid = false;
    }

    // Validate emergency contact phone
    final emergencyPhoneError = FormValidation.validatePhoneNumber(
      emergencyNomorHpController.text,
    );
    if (emergencyPhoneError != null) {
      emergencyNomorHpError.value = emergencyPhoneError;
      isValid = false;
    }

    // Validate bank account holder name
    final accountHolderError = FormValidation.validateRequired(
      namaPemilikRekeningController.text,
      'Nama pemilik rekening',
    );
    if (accountHolderError != null) {
      namaPemilikRekeningError.value = accountHolderError;
      isValid = false;
    }

    // Validate account number
    final accountNumberError = FormValidation.validateAccountNumber(
      nomorRekeningController.text,
    );
    if (accountNumberError != null) {
      nomorRekeningError.value = accountNumberError;
      isValid = false;
    }

    // Validate bank name
    final bankNameError = FormValidation.validateRequired(
      namaBankController.text,
      'Bank',
    );
    if (bankNameError != null || namaBankController.text == 'Pilih') {
      namaBankError.value = bankNameError ?? 'Silakan pilih bank';
      isValid = false;
    }

    return isValid;
  }

  void _clearAllErrors() {
    emailError.value = '';
    nomorHpError.value = '';
    emergencyNamaError.value = '';
    emergencyHubunganError.value = '';
    emergencyNomorHpError.value = '';
    namaPemilikRekeningError.value = '';
    nomorRekeningError.value = '';
    namaBankError.value = '';
  }

  // Legacy validation methods (kept for backward compatibility)
  bool isEmailValid() {
    if (emailController.text.isEmpty) return false;
    return GetUtils.isEmail(emailController.text);
  }

  bool isPhoneNumberValid() {
    if (nomorHpController.text.isEmpty) return false;
    // Basic phone number validation
    return nomorHpController.text.length >= 10;
  }

  bool isEmergencyContactValid() {
    return emergencyNamaController.text.isNotEmpty &&
        emergencyHubunganController.text.isNotEmpty &&
        emergencyNomorHpController.text.isNotEmpty &&
        emergencyNomorHpController.text.length >= 10;
  }

  bool isBankInfoValid() {
    return namaPemilikRekeningController.text.isNotEmpty &&
        nomorRekeningController.text.isNotEmpty &&
        namaBankController.text.isNotEmpty &&
        namaBankController.text != 'Pilih';
  }

  bool isFormValid() {
    return isEmailValid() &&
        isPhoneNumberValid() &&
        isEmergencyContactValid() &&
        isBankInfoValid();
  }

  // Method untuk clear semua field
  void clearAllFields() {
    emailController.clear();
    nomorHpController.clear();
    emergencyNamaController.clear();
    emergencyHubunganController.clear();
    emergencyNomorHpController.clear();
    namaPemilikRekeningController.clear();
    nomorRekeningController.clear();
    namaBankController.clear();
  }

  // Method untuk auto-fill bank account holder name dengan nama KTP
  void autoFillAccountHolderName(String ktpName) {
    if (namaPemilikRekeningController.text.isEmpty && ktpName.isNotEmpty) {
      namaPemilikRekeningController.text = ktpName;
    }
  }

  // Populate form data from model
  void populateFormData(RecruitmentFormModel formData) {
    // Isi FormSelfIdentification dengan data
    emailController.text = formData.email ?? '';
    nomorHpController.text = formData.nomorHp ?? '';
    emergencyNamaController.text = formData.emergencyNama ?? '';
    emergencyHubunganController.text = formData.emergencyHubungan ?? '';
    emergencyNomorHpController.text = formData.emergencyNomorHp ?? '';
    namaPemilikRekeningController.text = formData.namaPemilikRekening ?? '';
    namaBankController.text = formData.namaBank ?? '';
    nomorRekeningController.text = formData.nomorRekening ?? '';
  }

  @override
  void onClose() {
    // Dispose all controllers
    emailController.dispose();
    nomorHpController.dispose();
    emergencyNamaController.dispose();
    emergencyHubunganController.dispose();
    emergencyNomorHpController.dispose();
    namaPemilikRekeningController.dispose();
    nomorRekeningController.dispose();
    namaBankController.dispose();

    super.onClose();
  }
}
