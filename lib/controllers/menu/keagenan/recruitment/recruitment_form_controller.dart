import 'dart:async';
import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/controllers/menu/keagenan/recruitment/form_verification_controller.dart';
import 'package:pdl_superapp/controllers/menu/keagenan/recruitment/form_identification_controller.dart';
import 'package:pdl_superapp/controllers/menu/keagenan/recruitment/form_self_identification_controller.dart';
import 'package:pdl_superapp/models/recruitment_form_model.dart';
import 'package:pdl_superapp/utils/form_firestore_service.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';

class RecruitmentFormController extends BaseControllers {
  PageController pageController = PageController();
  RxInt activePage = 0.obs;

  RxBool isVerificationEmailSent = false.obs;

  late SharedPreferences prefs;

  // Form ID untuk Firestore
  RxString formId = ''.obs;

  // Status form
  RxBool isFormLoaded = false.obs;
  RxBool isFormSaving = false.obs;
  RxBool isFormSubmitted = false.obs;
  RxString formStatus = 'draft'.obs;

  // Timer untuk auto-save
  Timer? _autoSaveTimer;
  final int _autoSaveIntervalSeconds = 5; // Auto-save setiap 5 detik

  // Service untuk Firestore
  final FormFirestoreService _firestoreService = FormFirestoreService();

  // Sub-controllers
  late FormVerificationController verificationController;
  late FormIdentificationController identificationController;
  late FormSelfIdentificationController selfIdentificationController;

  RxBool isReady = false.obs;

  @override
  void onInit() async {
    super.onInit();
    prefs = await SharedPreferences.getInstance();

    // Initialize sub-controllers
    verificationController = Get.put(
      FormVerificationController(baseController: this),
    );
    identificationController = Get.put(
      FormIdentificationController(baseController: this),
    );
    selfIdentificationController = Get.put(
      FormSelfIdentificationController(baseController: this),
    );

    // Inisialisasi form ID atau ambil dari parameter jika ada
    _initFormId();
    // Setup listener untuk perubahan form
    _setupFormChangeListeners();
  }

  @override
  void onReady() {
    super.onReady();
    isReady.value = true;
  }

  // Delegate to sub-controllers for API responses
  @override
  void loadSuccess({
    required int requestCode,
    required response,
    required int statusCode,
  }) {
    super.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );
    // Delegate to appropriate sub-controller
    verificationController.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );
    selfIdentificationController.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );
  }

  // Load recruiter data from SharedPreferences
  void loadRecruiterDataFromPrefs() {
    verificationController.loadRecruiterDataFromPrefs();
  }

  // Inisialisasi form ID
  Future<void> _initFormId() async {
    // Cek apakah ada form ID dari parameter
    if (Get.parameters.containsKey('formId')) {
      formId.value = Get.parameters['formId']!;
      log('Menggunakan form ID dari parameter: ${formId.value}');
      // Load form data jika ada
      _loadFormData();
    } else {
      // Buat form ID baru menggunakan UUID
      formId.value = const Uuid().v4();
      log('Membuat form ID baru: ${formId.value}');
      loadRecruiterDataFromPrefs();
    }
  }

  // Setup listener untuk perubahan form
  void _setupFormChangeListeners() {
    // Setup listeners for sub-controllers to notify main controller
    // This will be handled by each sub-controller calling onFormChanged()
  }

  // Handler ketika form berubah - called by sub-controllers
  void onFormChanged() {
    // Jika timer sudah berjalan, reset
    _autoSaveTimer?.cancel();

    // Mulai timer baru untuk auto-save
    _autoSaveTimer = Timer(Duration(seconds: _autoSaveIntervalSeconds), () {
      // Simpan form data
      saveFormData();
    });
  }

  // Load form data dari Firestore
  Future<void> _loadFormData() async {
    if (formId.value.isEmpty) {
      log('Form ID kosong, tidak dapat memuat data form');
      return;
    }

    setLoading(true);

    try {
      final formData = await _firestoreService.getRecruitmentForm(formId.value);

      if (formData != null) {
        // Isi form dengan data yang ada
        _populateFormWithData(formData);
        isFormLoaded.value = true;
        log('Berhasil memuat data form dengan ID: ${formId.value}');
      } else {
        log('Tidak ada data form dengan ID: ${formId.value}');
      }
    } catch (e) {
      log('Error saat memuat data form: $e');
    } finally {
      setLoading(false);
    }
  }

  // Delegate form population to sub-controllers
  void _populateFormWithData(RecruitmentFormModel formData) {
    // Delegate to sub-controllers
    verificationController.populateFormData(formData);
    identificationController.populateFormData(formData);
    selfIdentificationController.populateFormData(formData);

    // Set status form
    isFormSubmitted.value = formData.isSubmitted ?? false;
    formStatus.value = formData.formStatus ?? 'draft';

    loadRecruiterDataFromPrefs();
  }

  // Simpan form data ke Firestore
  Future<bool> saveFormData({bool isSubmit = false}) async {
    if (formId.value.isEmpty) {
      log('Form ID kosong, tidak dapat menyimpan data form');
      return false;
    }

    isFormSaving.value = true;

    try {
      // Buat model dari data form saat ini
      final formData = _createFormModel(isSubmit: isSubmit);

      // Simpan ke Firestore
      final result = await _firestoreService.saveRecruitmentForm(
        formData,
        formId.value,
      );

      if (result) {
        log('Berhasil menyimpan data form dengan ID: ${formId.value}');
        if (isSubmit) {
          isFormSubmitted.value = true;
          formStatus.value = 'submitted';
        }
      } else {
        log('Gagal menyimpan data form dengan ID: ${formId.value}');
      }

      return result;
    } catch (e) {
      log('Error saat menyimpan data form: $e');
      return false;
    } finally {
      isFormSaving.value = false;
    }
  }

  // Buat model dari data form saat ini - delegate to sub-controllers
  RecruitmentFormModel _createFormModel({bool isSubmit = false}) {
    return RecruitmentFormModel(
      id: formId.value,

      // Get data from sub-controllers
      recruiterName: verificationController.recruiterName.value,
      recruiterId: verificationController.recruiterId.value,
      recruiterBranch: verificationController.recruiterBranch.value,
      recruiterCode: verificationController.recruiterCode.value,
      recruiterLevel: verificationController.recruiterLevel.value,
      recruiterPhoto: verificationController.recruiterPhoto.value,
      candidateLevel: verificationController.candidateLevelController.text,
      candidateBranch: verificationController.candidateBranchController.text,
      candidateBranchCode: verificationController.candidateBranchCode.value,

      // Data FormIdentification
      nik: identificationController.nikController.text,
      namaKtp: identificationController.namaKtpController.text,
      tempatLahir: identificationController.tempatLahirController.text,
      tanggalLahir: identificationController.tanggalLahirController.text,
      bulanLahir: identificationController.bulanLahirController.text,
      tahunLahir: identificationController.tahunLahirController.text,
      jenisKelamin: identificationController.jenisKelaminController.text,
      alamatKtp: identificationController.alamatKtpController.text,
      rtKtp: identificationController.rtKtpController.text,
      rwKtp: identificationController.rwKtpController.text,
      provinsiKtp: identificationController.provinsiKtpController.text,
      kabupatenKtp: identificationController.kabupatenKtpController.text,
      kecamatanKtp: identificationController.kecamatanKtpController.text,
      kelurahanKtp: identificationController.kelurahanKtpController.text,

      // Data Alamat Domisili
      alamatDomisili: identificationController.alamatDomisiliController.text,
      rtDomisili: identificationController.rtDomisiliController.text,
      rwDomisili: identificationController.rwDomisiliController.text,
      provinsiDomisili:
          identificationController.provinsiDomisiliController.text,
      kabupatenDomisili:
          identificationController.kabupatenDomisiliController.text,
      kecamatanDomisili:
          identificationController.kecamatanDomisiliController.text,
      kelurahanDomisili:
          identificationController.kelurahanDomisiliController.text,

      // Data FormSelfIdentification
      email: selfIdentificationController.emailController.text,
      nomorHp: selfIdentificationController.nomorHpController.text,
      emergencyNama: selfIdentificationController.emergencyNamaController.text,
      emergencyHubungan:
          selfIdentificationController.emergencyHubunganController.text,
      emergencyNomorHp:
          selfIdentificationController.emergencyNomorHpController.text,
      namaPemilikRekening:
          selfIdentificationController.namaPemilikRekeningController.text,
      nomorRekening: selfIdentificationController.nomorRekeningController.text,
      namaBank: selfIdentificationController.namaBankController.text,

      // Data Foto
      ktpImagePath: verificationController.ktpImage.value?.path,
      selfieKtpImagePath: verificationController.selfieKtpImage.value?.path,
      pasFotoImagePath: verificationController.pasFotoImage.value?.path,

      // Data URL Foto
      ktpImageUrl: verificationController.ktpUrl.value,
      selfieKtpImageUrl: verificationController.selfieKtpUrl.value,
      pasFotoImageUrl: verificationController.pasFotoUrl.value,

      // Metadata
      lastUpdated: DateTime.now().millisecondsSinceEpoch,
      isSubmitted: isSubmit ? true : isFormSubmitted.value,
      formStatus: isSubmit ? 'submitted' : formStatus.value,
    );
  }

  Future<bool> submitForm() async {
    return await saveFormData(isSubmit: true);
  }

  // Submit form
  Future<bool> submitFormForVerification() async {
    var data = {
      "recruiterCode": verificationController.recruiterCode.value,
      "ktpPhoto": verificationController.ktpUrl.value,
      "selfiePhoto": verificationController.selfieKtpUrl.value,
      "passPhoto": verificationController.pasFotoUrl.value,
      "positionLevel": verificationController.recruiterLevel.value,
      "branch": verificationController.candidateBranchCode.value,
      //TODO! BANK sekarang masih string, tapi key "value" isinya code tapi string tapi ada yang string kosong juga
      "bank": selfIdentificationController.namaBankController.text,
      "nik": identificationController.nikController.text,
      "fullName": identificationController.namaKtpController.text,
      "birthPlace": identificationController.tempatLahirController.text,
      "birthDate":
          "${identificationController.tahunLahirController.text}-${((identificationController.monthList.indexOf(identificationController.bulanLahirController.text) + 1))}-${identificationController.tanggalLahirController.text}",
      "gender":
          identificationController.selectedJenisKelamin.value == 0 ? 'M' : 'F',
      "ktpProvince": identificationController.provinsiKtpController.text,
      "ktpCity": identificationController.kabupatenKtpController.text,
      "ktpDistrict": identificationController.kecamatanKtpController.text,
      "ktpSubDistrict": identificationController.kelurahanKtpController.text,
      "ktpRt": identificationController.rtKtpController.text,
      "ktpRw": identificationController.rwKtpController.text,
      "ktpAddress": identificationController.alamatKtpController.text,
      "isDomicileSameAsKtp":
          identificationController.selectedIsAddressSame.value == 0
              ? true
              : false,
      "domicileProvince":
          identificationController.selectedIsAddressSame.value == 0
              ? identificationController.provinsiKtpController.text
              : identificationController.provinsiDomisiliController.text,
      "domicileCity":
          identificationController.selectedIsAddressSame.value == 0
              ? identificationController.kabupatenKtpController.text
              : identificationController.kabupatenDomisiliController.text,
      "domicileDistrict":
          identificationController.selectedIsAddressSame.value == 0
              ? identificationController.kecamatanKtpController.text
              : identificationController.kecamatanDomisiliController.text,
      "domicileSubDistrict":
          identificationController.selectedIsAddressSame.value == 0
              ? identificationController.kelurahanKtpController.text
              : identificationController.kelurahanDomisiliController.text,
      "domicileRt":
          identificationController.selectedIsAddressSame.value == 0
              ? identificationController.rtKtpController.text
              : identificationController.rtDomisiliController.text,
      "domicileRw":
          identificationController.selectedIsAddressSame.value == 0
              ? identificationController.rwKtpController.text
              : identificationController.rwDomisiliController.text,
      "domicileAddress":
          identificationController.selectedIsAddressSame.value == 0
              ? identificationController.alamatKtpController.text
              : identificationController.alamatDomisiliController.text,
      "phoneNumber": selfIdentificationController.nomorHpController.text,
      "maritalStatus": "",
      "occupation": "",
      "occupationCode": "",
      "email": selfIdentificationController.emailController.text,
      "emergencyContactName":
          selfIdentificationController.emergencyNamaController.text,
      "emergencyContactRelation":
          selfIdentificationController.emergencyHubunganController.text,
      "emergencyContactPhone":
          selfIdentificationController.emergencyNomorHpController.text,
      "bankAccountName":
          selfIdentificationController.namaPemilikRekeningController.text,
      "bankAccountNumber":
          selfIdentificationController.nomorRekeningController.text,
      "lastJob": "",
      "signature": "",
      "paraf": "",
      "last5YearJobData": [
        // {"year": 0, "company": "string", "position": "string"},
      ],
      "last2YearProductionData": [
        // {"personalProduction": 0, "teamProduction": 0},
      ],
      "lastCompanyManPowerData": {"agentCount": 0, "leaderCount": 0},
      "rewardInfoData": [
        // {"year": 0, "description": "string"},
      ],
    };
    print('data: $data');
    // 1. send data as draft
    // api.performSaveDraft(controllers: this, data: data, code: kReqSaveDraft);
    isVerificationEmailSent.value = true;
    return true;
  }

  // Delegate image picking to verification controller
  Future<void> pickKtpImage(String title, String type) async {
    await verificationController.pickKtpImage(title, type);
  }

  // Delegate image clearing to verification controller
  void clearImage(String type) {
    verificationController.clearImage(type);
  }

  // Delegate KTP OCR processing to identification controller
  Future<void> processKtpOcr(file) async {
    await identificationController.processKtpOcr(file);
  }

  // Delegate branch text change to verification controller
  void onBranchTextChanged(String value) {
    verificationController.onBranchTextChanged(value);
  }

  // Validate current page before navigation
  bool validateCurrentPage() {
    switch (activePage.value) {
      case 0:
        return verificationController.validateForm();
      case 1:
        return identificationController.validateForm();
      case 2:
        return selfIdentificationController.validateForm();
      default:
        return true;
    }
  }

  // Navigation methods
  void nextPage() {
    if (activePage.value < 2) {
      activePage.value++;
      pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void previousPage() {
    if (activePage.value > 0) {
      activePage.value--;
      pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void goToPage(int page) {
    if (page >= 0 && page <= 2) {
      activePage.value = page;
      pageController.animateToPage(
        page,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  @override
  void onClose() {
    // Batalkan timer auto-save
    _autoSaveTimer?.cancel();

    // Dispose page controller
    pageController.dispose();

    // Dispose sub-controllers
    Get.delete<FormVerificationController>();
    Get.delete<FormIdentificationController>();
    Get.delete<FormSelfIdentificationController>();

    super.onClose();
  }
}
