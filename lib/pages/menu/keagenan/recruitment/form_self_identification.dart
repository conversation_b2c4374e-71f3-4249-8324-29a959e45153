import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/components/pdl_drop_down.dart';
import 'package:pdl_superapp/components/pdl_text_field.dart';
import 'package:pdl_superapp/components/title_widget.dart';
import 'package:pdl_superapp/controllers/menu/keagenan/recruitment/form_self_identification_controller.dart';
import 'package:pdl_superapp/utils/constants.dart';

class FormSelfIdentification extends StatelessWidget {
  final FormSelfIdentificationController controller;

  const FormSelfIdentification({super.key, required this.controller});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TitleWidget(title: 'Kelengkapan Data Pribadi'),
        Text(
          'Mohon mengisi data dengan benar, tanpa kesalahan.',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: Get.isDarkMode ? kColorTextTersier : kColorTextTersierLight,
            height: 2,
          ),
        ),
        SizedBox(height: paddingMedium),
        _infoContact(context),
        SizedBox(height: paddingMedium),
        _emergencyContact(context),
        SizedBox(height: paddingMedium),
        _bankSection(context),
        SizedBox(height: paddingMedium),
      ],
    );
  }

  Column _infoContact(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Informasi & Kontak',
          style: Theme.of(
            context,
          ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w700),
        ),
        _padding(
          PdlTextField(
            label: 'Email',
            textController: controller.emailController,
          ),
        ),
        _padding(
          PdlTextField(
            label: 'Nomor HP',
            isPhoneNumber: true,
            textController: controller.nomorHpController,
          ),
        ),
      ],
    );
  }

  Column _emergencyContact(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Kontak Darurat',
          style: Theme.of(
            context,
          ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w700),
        ),
        _padding(
          PdlTextField(
            label: 'Nama',
            textController: controller.emergencyNamaController,
          ),
        ),
        _padding(
          PdlTextField(
            label: 'Hubungan Dengan Anda',
            textController: controller.emergencyHubunganController,
          ),
        ),
        _padding(
          PdlTextField(
            label: 'Nomor HP',
            isPhoneNumber: true,
            textController: controller.emergencyNomorHpController,
          ),
        ),
      ],
    );
  }

  Column _bankSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Nomor Rekening',
          style: Theme.of(
            context,
          ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w700),
        ),
        _padding(
          PdlTextField(
            label: 'Nama Pemilik Rekening',
            textController: controller.namaPemilikRekeningController,
          ),
        ),
        _padding(
          PdlTextField(
            label: 'Nomor Rekening',
            textController: controller.nomorRekeningController,
          ),
        ),
        _padding(
          PdlDropDown(
            item: [
              for (int i = 0; i < controller.bankList.length; i++)
                controller.bankList[i].value2 ?? '-',
              'Pilih',
            ],
            selectedItem:
                controller.namaBankController.text.isNotEmpty
                    ? controller.namaBankController.text
                    : controller.bankList[0].value2 ?? 'Pilih',
            title: 'Bank',
            enabled: true,
            onChanged: (val) => controller.namaBankController.text = val ?? '',
          ),
        ),
      ],
    );
  }

  Padding _padding(Widget child) {
    return Padding(
      padding: const EdgeInsets.only(top: paddingMedium),
      child: child,
    );
  }
}
