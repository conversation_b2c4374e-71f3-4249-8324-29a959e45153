import 'package:animated_custom_dropdown/custom_dropdown.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/components/image_form.dart';
import 'package:pdl_superapp/components/pdl_text_field.dart';
import 'package:pdl_superapp/components/title_widget.dart';
import 'package:pdl_superapp/controllers/menu/keagenan/recruitment/form_verification_controller.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:pdl_superapp/utils/utils.dart';
import 'package:shimmer/shimmer.dart';

class FormVerification extends StatelessWidget {
  final FormVerificationController controller;

  const FormVerification({super.key, required this.controller});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TitleWidget(title: 'Informasi Perekrut'),
        SizedBox(height: paddingMedium),
        _recruiterInformation(context),
        SizedBox(height: paddingSmall),
        Divider(),
        SizedBox(height: paddingMedium),
        _candidateLevel(context),
        SizedBox(height: paddingLarge),
        _cadidateIdentity(context),
      ],
    );
  }

  SizedBox _cadidateIdentity(BuildContext context) {
    return SizedBox(
      width: Get.width,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TitleWidget(title: 'Foto Identitas Kandidat'),
          SizedBox(height: paddingSmall),
          Text(
            'Pastikan foto yang diunggah telah sesuai dengan ketentuan perusahaan.',
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          SizedBox(height: paddingMedium),
          ImageForm(
            title: 'Foto KTP',
            imageFile: controller.ktpImage,
            imageUrl: controller.ktpUrl,
            onTapAdd: () => controller.pickKtpImage('KTP', kPhotoTypeKtp),
            description: 'Identitas sesuai KTP akan terisi secara otomatis',
            uploadText: 'Unggah atau ambil foto KTP',
            isUploading: controller.isKtpUploading,
            uploadProgress: controller.ktpUploadProgress,
            onClear: () => controller.clearImage(kPhotoTypeKtp),
          ),
          SizedBox(height: paddingMedium),
          ImageForm(
            title: 'Selfie Dengan KTP',
            imageFile: controller.selfieKtpImage,
            imageUrl: controller.selfieKtpUrl,
            onTapAdd:
                () => controller.pickKtpImage(
                  'Selfie Dengan KTP',
                  kPhotoTypeSelfieKtp,
                ),
            uploadText: 'Unggah atau ambil foto selfie dengan KTP',
            isUploading: controller.isSelfieKtpUploading,
            uploadProgress: controller.selfieKtpUploadProgress,
            onClear: () => controller.clearImage(kPhotoTypeSelfieKtp),
          ),
          SizedBox(height: paddingMedium),
          ImageForm(
            title: 'Pas Foto',
            imageFile: controller.pasFotoImage,
            imageUrl: controller.pasFotoUrl,
            onTapAdd:
                () => controller.pickKtpImage('Pas Foto', kPhotoTypePasFoto),
            uploadText: 'Unggah atau ambil pas foto',
            isUploading: controller.isPasFotoUploading,
            uploadProgress: controller.pasFotoUploadProgress,
            onClear: () => controller.clearImage(kPhotoTypePasFoto),
          ),
        ],
      ),
    );
  }

  Widget _candidateLevel(BuildContext context) {
    return Obx(() {
      if (controller.isAgentLoading.value == true) {
        return Shimmer.fromColors(
          baseColor: Colors.grey[300]!,
          highlightColor: Colors.grey[100]!,
          child: Container(
            width: Get.width,
            height: 40,
            decoration: BoxDecoration(
              color: kColorBgLight,
              borderRadius: BorderRadius.circular(radiusSmall),
            ),
          ),
        );
      } else {
        return SizedBox(
          width: Get.width,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              TitleWidget(title: 'Level Keagenan Kandidat'),
              SizedBox(height: paddingMedium),
              Text('Level', style: Theme.of(context).textTheme.bodyMedium),
              SizedBox(height: paddingSmall),
              Theme(
                data: Utils.dropDownThemeData(context),
                child: CustomDropdown(
                  items: [
                    for (int i = 0; i < controller.roleCandidate.length; i++)
                      controller.roleCandidate[i],
                  ],
                  initialItem: 'BP',
                  // controller.candidateLevelController.text.isNotEmpty
                  //     ? controller.candidateLevelController.text
                  //     : controller.roleCandidate[0],
                  onChanged: (val) {
                    if (val != null) {
                      controller.candidateLevelController.text = val;
                    }
                  },
                  closedHeaderPadding: EdgeInsets.all(12),
                  decoration: Utils.dropdownDecoration(context),
                ),
              ),
              SizedBox(height: paddingMedium),
              Obx(
                () => PdlTextField(
                  textController: controller.candidateBranchController,
                  hint: 'Cari Kantor Cabang',
                  label: 'Kantor Cabang',
                  onChanged: (val) => controller.onBranchTextChanged(val),
                  prefixIcon: Icon(Icons.search),
                  hasError: controller.candidateBranchError.value.isNotEmpty,
                  errorText:
                      controller.candidateBranchError.value.isEmpty
                          ? null
                          : controller.candidateBranchError.value,
                  items: [
                    for (int i = 0; i < controller.branchList.length; i++)
                      GestureDetector(
                        onTap: () {
                          controller.candidateBranchController.text =
                              controller.branchList[i].branchName ?? '-';
                          controller.candidateBranchCode.value =
                              controller.branchList[i].id ?? 0;
                          controller.branchList.clear();
                        },
                        child: Container(
                          width: Get.width,
                          color: Colors.transparent,
                          padding: EdgeInsets.only(top: paddingSmall),
                          child: Text(
                            controller.branchList[i].branchName ?? '-',
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ],
          ),
        );
      }
    });
  }

  Widget _recruiterInformation(BuildContext context) {
    return Obx(() {
      if (controller.state.value == ControllerState.loading) {
        return CircularProgressIndicator();
      }
      return Row(
        children: [
          SizedBox(
            width: 50,
            height: 50,
            child: CircleAvatar(
              backgroundImage:
                  controller.recruiterPhoto.value.isNotEmpty
                      ? NetworkImage(controller.recruiterPhoto.value)
                          as ImageProvider
                      : null,
              child:
                  controller.recruiterPhoto.value.isEmpty
                      ? Text(Utils.getInitials(controller.recruiterName.value))
                      : null,
            ),
          ),
          SizedBox(width: paddingSmall),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Wrap(
                  crossAxisAlignment: WrapCrossAlignment.center,
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        color: kColorGlobalBgBlue,
                        borderRadius: BorderRadius.circular(paddingMedium),
                      ),
                      padding: EdgeInsets.all(paddingSmall),
                      child: Text(
                        controller.recruiterLevel.value,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: kColorGlobalBlue,
                        ),
                      ),
                    ),
                    SizedBox(width: paddingSmall),
                    Text(
                      controller.recruiterName.value,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                  ],
                ),
                SizedBox(
                  width: Get.width,
                  child: Text(
                    '${controller.recruiterCode.value} - ${controller.recruiterBranch.value}',
                    style: Theme.of(
                      context,
                    ).textTheme.bodyMedium?.copyWith(height: 2),
                  ),
                ),
              ],
            ),
          ),
        ],
      );
    });
  }
}
